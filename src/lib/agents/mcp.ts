import { MCPClient } from "@mastra/mcp";

export const cryptoMCP = new MCPClient({
  id: "cryptoMCP",
  servers: {
    iotexScan: {
      url: new URL("https://api-quicksilver.iotex.ai/uk2itcjlt5m7vlkbqfb9542g/sse?authorization=1f7cbe17-d58d-44ac-b347-54861d059321")
    },
    coingecko: {
      url: new URL("https://api-quicksilver.iotex.ai/r5u7hxl7e4s17nphgaod2cyn/sse?authorization=1f7cbe17-d58d-44ac-b347-54861d059321")
    }
  },
});

export const generalMCP = new MCPClient({
  id: "generalMCP",
  servers: {
    weather: {
      url: new URL("https://api-quicksilver.iotex.ai/m0fz1z26ogtxpfo0ni2yuikj/sse?authorization=1f7cbe17-d58d-44ac-b347-54861d059321")
    },
  },
});

