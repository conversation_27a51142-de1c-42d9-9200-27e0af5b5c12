import { Agent } from "@mastra/core/agent";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@mastra/core";
import { cryptoResearchAgent } from "@/lib/agents/crypto-research-expert";
import { RouterAgent } from "@/lib/agents/router-agent";
import { GeneralAgent } from "@/lib/agents/general-agent";
import { logInfo } from "@/action";
import { Message } from "@/types/types";
import { cryptoMCP, generalMCP } from "./mcp";
import { MCPClient } from "@mastra/mcp";

const mastra = new Mastra({
  agents: { RouterAgent, GeneralAgent, cryptoResearchAgent },
});

export class AgentDelegate {

  constructor() {
  }

  async sendMessageToChat(messages: Message[], address: string, reasoning: boolean) {

    const message = messages[messages.length - 1]
    console.log("question: ", JSON.stringify(message))

    const routing = await this.agentRouter(message, address, process.env.DIFY_ROUTER)
    console.log("router: ", routing)

    if (routing.includes("IoTeX Agent")) {
      console.log("IoTeX Agent")
      logInfo(address, message.content, reasoning, "IoTeX Agent")
      return this.agentApiStream(message, address, process.env.DIFY_IOPAY_EXPORT)
    } else if (routing.includes("Crypto Agent")) {
      console.log("Crypto Agent")
      logInfo(address, message.content, reasoning, "Crypto Agent")
      // @ts-ignore
      return this.agentStream(mastra.getAgent("cryptoResearchAgent"), messages, cryptoMCP)
    } else {
      console.log("GeneralAgent")
      logInfo(address, message.content, reasoning, "GeneralAgent")
      // @ts-ignore
      return this.agentStream(mastra.getAgent("GeneralAgent"), messages, generalMCP)
    }
  }

  async agentStream(agent: Agent, messages: CoreMessage[], mcp?: MCPClient) {
    const toolsets = await mcp?.getToolsets()
    const response = await agent.stream(messages, {
      toolsets: toolsets
    })
    return new ReadableStream({
      async start(controller) {
        for await (const chunk of response.fullStream) {
          const jsonData = JSON.stringify([chunk]);
          const sseData = `data: ${jsonData}\n\n`;
          controller.enqueue(new TextEncoder().encode(sseData));
        }
        const finishData = `data: ${JSON.stringify([{
          type: "finish",
        }])}\n\n`;
        controller.enqueue(new TextEncoder().encode(finishData));
        controller.close();
        // await mcp?.disconnect()
      },
    });
  }

  async agentRouter(message: Message, address: string, key: string) {
    try {
      const response = await fetch("https://dify.iotex.one/v1/chat-messages", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Connection": "keep-alive",
          "Authorization": `Bearer ${key}`
        },
        body: JSON.stringify({
          inputs: {},
          query: message.content,
          response_mode: "blocking",
          user: address,
        })
      });

      const result = await response.json();
      console.log(JSON.stringify(result))
      return result.answer
    } catch (error) {
      return "Crypto Agent"
    }
  }

  async agentApiStream(message: Message, address: string, key: string) {
    const response = await fetch("https://dify.iotex.one/v1/chat-messages", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Connection": "keep-alive",
        "Authorization": `Bearer ${key}`
      },
      body: JSON.stringify({
        inputs: {},
        query: message.content,
        response_mode: "streaming",
        // conversation_id: message.conversation_id ?? "",
        user: address,
      })
    })

    if (!response.ok) {
      throw new Error(`HTTPS error! status: ${response.status}`);
    }

    if (!response.body) {
      throw new Error('ReadableStream not supported in this browser.');
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';
    return new ReadableStream({
      async start(controller) {
        try {
          while (true) {
            const {done, value} = await reader.read();

            if (done) {
              console.log('Stream complete');
              const finishData = `data: ${JSON.stringify([{
                type: "finish",
              }])}\n\n`;
              controller.enqueue(new TextEncoder().encode(finishData));
              controller.close();
              break;
            }

            buffer += decoder.decode(value, {stream: true});
            const lines = buffer.split('\n');
            buffer = lines.pop();
            for (const line of lines) {
              if (!line.startsWith('data:')) continue;
              try {
                const data = JSON.parse(line.slice(5).trim());
                if (data.answer) {
                  const sseData = `data: ${JSON.stringify([{
                    type: "text-delta",
                    textDelta: data.answer,
                    conversation_id: data.conversation_id
                  }])}\n\n`;
                  controller.enqueue(new TextEncoder().encode(sseData));
                }
              } catch (error) {
                console.error("Failed to parse:", line);
              }
            }
          }
        } catch (error) {
          controller.error(new Error("Failed to process chat request"));
        }
      },
      cancel() {
        reader.releaseLock();
      }
    })
  }
}




