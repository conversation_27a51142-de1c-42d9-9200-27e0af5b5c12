/* Modern Loading Animation - BinoAI Style */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
}

.loading-logo {
    width: 4rem;
    height: 4rem;
    position: relative;
    animation: logoFloat 3s ease-in-out infinite;
}

.loading-logo::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    border-radius: 50%;
    opacity: 0.2;
    animation: logoPulse 2s ease-in-out infinite;
}

.loading-dots {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.loading-dot {
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    animation: dotBounce 1.4s ease-in-out infinite both;
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.loading-dot:nth-child(1) {
    animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
    animation-delay: -0.16s;
}

.loading-dot:nth-child(3) {
    animation-delay: 0s;
}

.loading-text {
    font-size: 0.875rem;
    font-weight: 500;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: textFade 2s ease-in-out infinite;
    letter-spacing: 0.05em;
}

.loading-progress {
    width: 12rem;
    height: 0.25rem;
    background: rgba(59, 130, 246, 0.1);
    border-radius: 0.125rem;
    overflow: hidden;
    position: relative;
}

.loading-progress::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, #3b82f6, #8b5cf6, transparent);
    animation: progressSlide 2s ease-in-out infinite;
}

/* Keyframe Animations */
@keyframes logoFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-10px) rotate(180deg);
    }
}

@keyframes logoPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.2;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.4;
    }
}

@keyframes dotBounce {
    0%, 80%, 100% {
        transform: scale(0.8) translateY(0);
        opacity: 0.6;
    }
    40% {
        transform: scale(1.2) translateY(-10px);
        opacity: 1;
    }
}

@keyframes textFade {
    0%, 100% {
        opacity: 0.7;
    }
    50% {
        opacity: 1;
    }
}

@keyframes progressSlide {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Dark mode adjustments */
.dark .loading-progress {
    background: rgba(59, 130, 246, 0.15);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .loading-logo {
        width: 3rem;
        height: 3rem;
    }

    .loading-progress {
        width: 10rem;
    }

    .loading-text {
        font-size: 0.8rem;
    }
}
