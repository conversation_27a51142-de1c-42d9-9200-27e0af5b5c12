.ChatA<PERSON>,
.MessageContainer,
.Navbar,
.Message .Bubble,
.QuickReplies,
.ChatFooter {
  background-repeat: no-repeat;
  background-size: cover;
}

.ChatApp {
  background: transparent;
  height: 0;
  flex: 1;
}

.<PERSON>t<PERSON>ooter {
  background: none;
}

.Message.left .Bubble {
  margin-right: 0;
  background: none;
}

.MessageContainer {
  background: transparent;
  padding: 1rem;
}

/* TypingBubble styles */
.TypingBubble {
  line-height: 1.7;
  margin: 0;
  padding: 0;
}

.TypingBubble p {
  margin-bottom: 0.75rem;
}

.TypingBubble p:last-child {
  margin-bottom: 0;
}

.Bubble.richtext {
  padding: 1rem;
}

.Input {
  background: none;
  border: none;
  color: var(--primary-text);
  font-size: 1rem;
  margin-right: 0.5rem;
}

.Typing-text {
  color: var(--primary-text);
  font-size: 1rem;
  font-weight: 500;
  padding: 0;
  font-family: "SFMono-Regular", <PERSON><PERSON><PERSON>, "Liberation Mono", Menlo, monospace;
}

.Message-main .Avatar {
  display: none;
}

:root {
  --color-fill-1: var(--white);
}

.dark {
  --color-fill-1: var(--black);
}

.slide-in-right-item {
  display: none;
}

@media (min-width: 768px) {
  .PullToRefresh-inner {
    width: 50%;
    margin: auto;
  }

  .Message.left .Bubble {
    max-width: 100%;
  }

  .Message-main .Avatar {
    display: flex;
  }

  .Bubble.richtext {
    padding: 1rem;
  }
}
