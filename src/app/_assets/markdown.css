@media (min-width: 768px) {
    .binoai-markdown {
        padding: 0 1rem 1rem;
    }
}

.binoai-markdown {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    line-height: 1.6;
    color: var(--primary-text);
    max-width: 100%;
    word-wrap: break-word;
}

.binoai-markdown :first-child {
    margin-top: 0;
}

.binoai-markdown h1 {
    font-size: 2em;
    margin: 0.67em 0;
    padding-bottom: 0.3em;
    border-bottom: 1px solid #eaecef;
    font-weight: 600;
}

.binoai-markdown h2 {
    font-size: 1.5em;
    margin: 0.83em 0;
    padding-bottom: 0.3em;
    border-bottom: 1px solid #eaecef;
    font-weight: 600;
}

.binoai-markdown h3 {
    font-size: 1.25em;
    margin: 1em 0;
    font-weight: 600;
}

.binoai-markdown h4 {
    font-size: 1em;
    margin: 1em 0;
    font-weight: 600;
}

.binoai-markdown h5 {
    font-size: 0.875em;
    margin: 0.75em 0;
    font-weight: 600;
}

.binoai-markdown h6 {
    font-size: 0.85em;
    margin: 0.5em 0;
    color: #666;
    font-weight: 600;
}

.binoai-markdown p {
    margin: 0 0 16px 0;
}

.binoai-markdown br {
    display: block;
    content: "";
    margin: 8px 0;
}

.binoai-markdown ul,
.binoai-markdown ol {
    margin: 0 0 16px 0;
    padding-left: 2em;
}

.binoai-markdown li {
    margin-bottom: 0.5em;
}

.binoai-markdown li > p {
    margin: 0;
}

.binoai-markdown ul ul,
.binoai-markdown ul ol,
.binoai-markdown ol ul,
.binoai-markdown ol ol {
    margin: 0;
}

.binoai-markdown pre {
    border-radius: 6px;
    padding: 16px;
    overflow: auto;
    margin: 0 0 16px 0;
}

.binoai-markdown code {
    font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
    font-size: 0.85rem;
}

.binoai-markdown pre code {
    background: none;
    padding: 0;
    border-radius: 0;
}

.binoai-markdown table {
    border-collapse: collapse;
    width: 100%;
    margin: 0 0 16px 0;
}

.binoai-markdown th,
.binoai-markdown td {
    padding: 6px 13px;
    border: 1px solid #dfe2e5;
}

.binoai-markdown th {
    font-weight: 600;
}

.binoai-markdown blockquote {
    margin: 0 0 16px 0;
    padding: 0 1em;
    color: var(--color-gray-400);
    border-left: 4px solid #dfe2e5;
}

.binoai-markdown hr {
    height: 1px;
    background-color: #e1e4e8;
    border: none;
    margin: 16px 0;
}

.binoai-markdown a {
    color: #0969da;
    text-decoration: none;
}

.binoai-markdown a:hover {
    text-decoration: underline;
}

.binoai-markdown img {
    max-width: 100%;
    height: auto;
}

.binoai-markdown strong {
    font-weight: 600;
}

.binoai-markdown em {
    font-style: italic;
}

.binoai-markdown del {
    text-decoration: line-through;
}

.binoai-markdown .task-list-item {
    list-style-type: none;
}

.binoai-markdown .task-list-item-checkbox {
    margin: 0 0.2em 0.25em -1.6em;
    vertical-align: middle;
}