@media (min-width: 768px) {
    .binoai-markdown {
        padding: 0;
    }
}

.binoai-markdown {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    line-height: 1.7;
    color: var(--primary-text);
    max-width: 100%;
    word-wrap: break-word;
    font-size: 0.95rem;
}

.binoai-markdown :first-child {
    margin-top: 0;
}

.binoai-markdown h1 {
    font-size: 1.75em;
    margin: 1.2em 0 0.8em 0;
    padding-bottom: 0.4em;
    border-bottom: 2px solid rgba(59, 130, 246, 0.3);
    font-weight: 700;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.binoai-markdown h2 {
    font-size: 1.4em;
    margin: 1em 0 0.6em 0;
    padding-bottom: 0.3em;
    border-bottom: 1px solid rgba(59, 130, 246, 0.2);
    font-weight: 650;
    color: #3b82f6;
}

.dark .binoai-markdown h2 {
    color: #60a5fa;
}

.binoai-markdown h3 {
    font-size: 1.2em;
    margin: 0.8em 0 0.5em 0;
    font-weight: 650;
    color: #6366f1;
}

.dark .binoai-markdown h3 {
    color: #818cf8;
}

.binoai-markdown h4 {
    font-size: 1.05em;
    margin: 0.7em 0 0.4em 0;
    font-weight: 600;
    color: #8b5cf6;
}

.dark .binoai-markdown h4 {
    color: #a78bfa;
}

.binoai-markdown h5 {
    font-size: 0.95em;
    margin: 0.6em 0 0.3em 0;
    font-weight: 600;
    color: #6b7280;
}

.dark .binoai-markdown h5 {
    color: #9ca3af;
}

.binoai-markdown h6 {
    font-size: 0.9em;
    margin: 0.5em 0 0.2em 0;
    color: #6b7280;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.dark .binoai-markdown h6 {
    color: #9ca3af;
}

.binoai-markdown p {
    margin: 0 0 1.2em 0;
    line-height: 1.7;
}

.binoai-markdown br {
    display: block;
    content: "";
    margin: 0.5em 0;
}

.binoai-markdown ul,
.binoai-markdown ol {
    margin: 0 0 1.2em 0;
    padding-left: 1.8em;
}

.binoai-markdown ul {
    list-style-type: none;
}

.binoai-markdown ul > li::before {
    content: "•";
    color: #3b82f6;
    font-weight: bold;
    position: absolute;
    margin-left: -1.2em;
}

.binoai-markdown ol {
    counter-reset: list-counter;
}

.binoai-markdown ol > li {
    counter-increment: list-counter;
    position: relative;
}

.binoai-markdown ol > li::before {
    content: counter(list-counter) ".";
    color: #3b82f6;
    font-weight: 600;
    position: absolute;
    margin-left: -1.8em;
    width: 1.5em;
    text-align: right;
}

.binoai-markdown li {
    margin-bottom: 0.6em;
    position: relative;
    padding-left: 0.2em;
}

.binoai-markdown li > p {
    margin: 0 0 0.4em 0;
}

.binoai-markdown ul ul,
.binoai-markdown ul ol,
.binoai-markdown ol ul,
.binoai-markdown ol ol {
    margin: 0.4em 0 0 0;
}

.binoai-markdown pre {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid rgba(59, 130, 246, 0.1);
    border-radius: 12px;
    padding: 1.2em;
    overflow: auto;
    margin: 1.2em 0;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    position: relative;
}

.dark .binoai-markdown pre {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.binoai-markdown pre::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6);
    border-radius: 12px 12px 0 0;
}

.binoai-markdown code {
    font-family: "JetBrains Mono", "Fira Code", "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
    font-size: 0.875rem;
    background: rgba(59, 130, 246, 0.1);
    padding: 0.2em 0.4em;
    border-radius: 6px;
    border: 1px solid rgba(59, 130, 246, 0.2);
    color: #3b82f6;
    font-weight: 500;
}

.dark .binoai-markdown code {
    background: rgba(59, 130, 246, 0.15);
    border: 1px solid rgba(59, 130, 246, 0.3);
    color: #60a5fa;
}

.binoai-markdown pre code {
    background: none;
    padding: 0;
    border-radius: 0;
    border: none;
    color: inherit;
    font-weight: normal;
}

.binoai-markdown table {
    border-collapse: collapse;
    width: 100%;
    margin: 1.5em 0;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(59, 130, 246, 0.1);
}

.dark .binoai-markdown table {
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.binoai-markdown th,
.binoai-markdown td {
    padding: 0.75em 1em;
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
    text-align: left;
}

.dark .binoai-markdown th,
.dark .binoai-markdown td {
    border-bottom: 1px solid rgba(59, 130, 246, 0.2);
}

.binoai-markdown th {
    font-weight: 650;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
    color: #3b82f6;
    font-size: 0.9em;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.dark .binoai-markdown th {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(139, 92, 246, 0.15) 100%);
    color: #60a5fa;
}

.binoai-markdown tr:nth-child(even) td {
    background: rgba(59, 130, 246, 0.02);
}

.dark .binoai-markdown tr:nth-child(even) td {
    background: rgba(59, 130, 246, 0.05);
}

.binoai-markdown tr:hover td {
    background: rgba(59, 130, 246, 0.05);
    transition: background-color 0.2s ease;
}

.dark .binoai-markdown tr:hover td {
    background: rgba(59, 130, 246, 0.1);
}

.binoai-markdown blockquote {
    margin: 1.5em 0;
    padding: 1em 1.2em;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
    border-left: 4px solid #3b82f6;
    border-radius: 0 8px 8px 0;
    color: #4b5563;
    font-style: italic;
    position: relative;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.dark .binoai-markdown blockquote {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
    color: #d1d5db;
    border-left-color: #60a5fa;
}

.binoai-markdown blockquote::before {
    content: "\201C";
    font-size: 3em;
    color: #3b82f6;
    position: absolute;
    top: -0.2em;
    left: 0.3em;
    opacity: 0.3;
    font-family: Georgia, serif;
}

.binoai-markdown hr {
    height: 2px;
    background: linear-gradient(90deg, transparent, #3b82f6, transparent);
    border: none;
    margin: 2em 0;
    border-radius: 1px;
}

.binoai-markdown a {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 500;
    border-bottom: 1px solid transparent;
    transition: all 0.2s ease;
}

.dark .binoai-markdown a {
    color: #60a5fa;
}

.binoai-markdown a:hover {
    color: #1d4ed8;
    border-bottom-color: #3b82f6;
    text-decoration: none;
}

.dark .binoai-markdown a:hover {
    color: #93c5fd;
    border-bottom-color: #60a5fa;
}

.binoai-markdown img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    margin: 1em 0;
    border: 1px solid rgba(59, 130, 246, 0.1);
}

.dark .binoai-markdown img {
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.binoai-markdown strong {
    font-weight: 650;
    color: #1f2937;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
    padding: 0.1em 0.2em;
    border-radius: 4px;
}

.dark .binoai-markdown strong {
    color: #f9fafb;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(139, 92, 246, 0.15));
}

.binoai-markdown em {
    font-style: italic;
    color: #6366f1;
    font-weight: 500;
}

.dark .binoai-markdown em {
    color: #818cf8;
}

.binoai-markdown del {
    text-decoration: line-through;
    color: #9ca3af;
    opacity: 0.7;
}

.binoai-markdown .task-list-item {
    list-style-type: none;
    position: relative;
    padding-left: 1.8em;
}

.binoai-markdown .task-list-item::before {
    display: none;
}

.binoai-markdown .task-list-item-checkbox {
    position: absolute;
    left: 0;
    top: 0.2em;
    margin: 0;
    width: 1.2em;
    height: 1.2em;
    accent-color: #3b82f6;
    cursor: pointer;
}

/* Additional modern styling */
.binoai-markdown mark {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(139, 92, 246, 0.2));
    color: inherit;
    padding: 0.1em 0.3em;
    border-radius: 4px;
}

.binoai-markdown kbd {
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    color: #374151;
    font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
    font-size: 0.85em;
    padding: 0.2em 0.4em;
}

.dark .binoai-markdown kbd {
    background: #374151;
    border: 1px solid #4b5563;
    color: #f9fafb;
}