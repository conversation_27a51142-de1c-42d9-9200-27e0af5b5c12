@import "tailwindcss";
@tailwind utilities;

@custom-variant dark (&:where(.dark, .dark *));

:root {
    --color-bg: theme('colors.white');
    --primary-text: theme('colors.black');
}

.dark {
    --color-bg: theme('colors.black');
    --primary-text: theme('colors.white');
}

body {
    background-color: var(--color-bg);
    font-family: Arial, Helvetica, sans-serif;
    font-size: 1rem;
    color: var(--primary-text);
}

ul, ol {
    list-style-type: disc;
}

/* Carousel Swiper Styles */
.carousel-swiper {
    overflow: visible !important;
}

.carousel-swiper .swiper-wrapper {
    align-items: center;
}

.carousel-swiper .swiper-slide {
    height: auto !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    opacity: 1 !important;
    transform: none !important;
}

.carousel-swiper .swiper-slide-active {
    z-index: 1;
}

.carousel-swiper .swiper-slide:not(.swiper-slide-active) {
    z-index: 0;
}