import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./_assets/globals.css";
import Root from "@/components/Root/Root";
import "@rainbow-me/rainbowkit/styles.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "ioPay binoAI",
  description: "AI-optimized Web3 exploration",
  keywords: [],
  icons: {
    icon: "https://iopay.me/images/iopay/logo_binoai.svg"
  },
  openGraph: {
    title: 'ioPay binoAI',
    description: 'AI-optimized Web3 exploration',
    images: 'https://iopay.me/images/iopay/logo_binoai.svg',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'ioPay binoAI',
    description: 'AI-optimized Web3 exploration',
    images: 'https://iopay.me/images/iopay/logo_binoai.svg',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <script defer data-domain="binoai.iopay.me/chat" src="https://ga.dapp.works/js/script.js"></script>
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
      <Root>
        {children}
      </Root>
      </body>
    </html>
  );
}
