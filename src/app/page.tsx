'use client';

import Chat, { Bubble, Input, Typing, TypingBubble, useMessages } from "@chatui/core";
import { useDarkTheme } from "@/hooks/useDarkTheme";
import { ArrowLeft, CircleStop, Copy, RefreshCcw, Trash2 } from "lucide-react";
import { marked } from "marked";
import { MessageProps } from "@chatui/core/lib/components/Message/Message";
import "@chatui/core/dist/index.css";
import "./_assets/chatui.css"
import "./_assets/markdown.css"
import React, { useCallback, useEffect, useRef, useState } from "react";
import { AnimatePresence, motion } from "framer-motion";
import { useAccount, useConnect } from "wagmi";
import { useConnectModal } from "@rainbow-me/rainbowkit";
import { sendChat } from "@/action";
import { createParser, EventSourceMessage } from "eventsource-parser";
import { ConnectWallet } from "@/components/ConnectWallet/ConnectWallet";
import { Button } from "@/components/Button/button";
import { MessageContainerHandle } from "@chatui/core/lib/components/MessageContainer";
import Prism from "prismjs";
import 'prismjs/themes/prism-tomorrow.css';
import toast from "react-hot-toast";
import Carousel from "@/components/carousel";
import { throttle } from 'lodash';

marked.use({
  async: false,
  extensions: [{
    name: "code",
    level: 'block',
    renderer: function (token) {
      const {lang, text} = token
      const language = Prism.languages[lang] ? lang : 'plaintext'
      const highlighted = Prism.languages[language]
        ? Prism.highlight(text, Prism.languages[language], language)
        : token.text;
      return `<pre class="language-${language}"><code>${highlighted}</code></pre>`;
    }
  }],
  walkTokens(token) {
    if (token.type === 'code') {
      return (async () => {
        if (token.lang && !Prism.languages[token.lang]) {
          await import(`prismjs/components/prism-${token.lang}`);
        }
      })();
    }
  }
})

function Navbar(props: {
  onBackHome: () => void,
  onClear: () => void
}) {
  const isDarkMode = useDarkTheme()
  return <div className="">
    <div
      className="hidden bg-white/80 dark:bg-gray-900/80 backdrop-blur-md md:flex items-center justify-between px-6 py-4 border-b border-gray-200/60 dark:border-gray-700/60 shadow-sm">
      <div className="flex items-center gap-4 cursor-pointer group" onClick={props.onBackHome}>
        <div
          className="hidden md:inline-flex items-center justify-center cursor-pointer relative"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full blur-lg opacity-20 group-hover:opacity-30 transition-opacity duration-300"></div>
          <img className="size-10 relative z-10 drop-shadow-sm" src="/images/binoai-logo.svg" alt=""/>
        </div>
        <div className="cursor-pointer">
          <div className="text-base font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 dark:from-white dark:via-blue-200 dark:to-purple-200 bg-clip-text text-transparent">
            ioPay BinoAI
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-300 font-medium">
            Your intelligent Web3 companion
          </div>
        </div>
      </div>
      <div className="flex items-center gap-3">
        <Button
          onClick={props.onClear}
          variant="outline"
          size="sm"
          className="flex items-center gap-2 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm text-gray-700 dark:text-gray-200 border-gray-200/60 dark:border-gray-700/60 hover:border-red-300/60 dark:hover:border-red-600/60 rounded-lg px-3 py-2 h-9 text-sm font-medium shadow-md shadow-black/5 dark:shadow-black/20 hover:shadow-lg hover:shadow-red-500/20 transition-all duration-300 hover:scale-105"
        >
          <Trash2 className="size-4"/>
          Clear
        </Button>
        <Button
          onClick={props.onBackHome}
          variant="outline"
          size="sm"
          className="flex items-center gap-2 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm text-gray-700 dark:text-gray-200 border-gray-200/60 dark:border-gray-700/60 hover:border-blue-300/60 dark:hover:border-blue-600/60 rounded-lg px-3 py-2 h-9 text-sm font-medium shadow-md shadow-black/5 dark:shadow-black/20 hover:shadow-lg hover:shadow-blue-500/20 transition-all duration-300 hover:scale-105"
        >
          <ArrowLeft className="size-4"/>
          Home
        </Button>
      </div>
    </div>
    <div className="flex md:hidden bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm items-center justify-between h-14 px-4 border-b border-gray-200/60 dark:border-gray-700/60 shadow-sm">
      <Button
        onClick={props.onBackHome}
        variant="outline"
        size="sm"
        className="flex items-center gap-2 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm text-gray-700 dark:text-gray-200 border-gray-200/60 dark:border-gray-700/60 rounded-lg px-2 py-1.5 h-8 text-sm font-medium shadow-md shadow-black/5 dark:shadow-black/20"
      >
        <ArrowLeft className="size-4"/>
      </Button>
      <div className="text-base font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 dark:from-white dark:via-blue-200 dark:to-purple-200 bg-clip-text text-transparent">ioPay BinoAI</div>
      <Button
        onClick={props.onClear}
        variant="outline"
        size="sm"
        className="flex items-center gap-2 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm text-gray-700 dark:text-gray-200 border-gray-200/60 dark:border-gray-700/60 rounded-lg px-2 py-1.5 h-8 text-sm font-medium shadow-md shadow-black/5 dark:shadow-black/20"
      >
        <Trash2 className="size-4"/>
      </Button>
    </div>
  </div>
}

function MessageContent(props: MessageProps & {
  isLoading: boolean,
  onRender?: () => void,
  resend?: (id: string) => void
}) {
  const {_id, type, content, isLoading, onRender, resend} = props;
  const isDarkMode = useDarkTheme()

  const throttledOnRender = throttle(onRender, 2000, {trailing: true});

  function renderMarkdown(text) {
    const html = marked(text, {
      async: false,
      gfm: true,
      breaks: true,
    })
    throttledOnRender()
    return html;
  }

  switch (type) {
    case "user":
      return <Bubble
        className="break-words px-5 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-2xl shadow-lg shadow-blue-500/25 whitespace-pre-wrap font-medium"
      >
        {content.text}
      </Bubble>;
    case "assistant":
      return <div className="binoai-markdown bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-2xl p-4 border border-gray-200/50 dark:border-gray-700/50 shadow-lg shadow-black/5 dark:shadow-black/20">
        {
          !content.text
            ? (isLoading && <Typing text="Thinking"/>)
            : <div>
              <TypingBubble
                className="text-gray-800 dark:text-gray-100 leading-relaxed mb-1"
                content={content.text}
                isRichText
                options={{
                  interval: 50,
                  step: [4, 8],
                  initialIndex: 10
                }}
                messageRender={renderMarkdown}
              />
              {
                (content.finish || !isLoading) &&
                  <div className="flex items-center gap-2 p-4 border-t border-gray-200/50 dark:border-gray-700/50">
                      <Button
                        onClick={async () => {
                          await navigator.clipboard.writeText(content.text);
                          toast.success("Copied!");
                        }}
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-1.5 bg-white/80 dark:bg-gray-700/80 backdrop-blur-sm text-gray-600 dark:text-gray-300 border-gray-200/60 dark:border-gray-600/60 hover:border-blue-300/60 dark:hover:border-blue-500/60 rounded-lg px-2.5 py-1.5 h-7 text-xs font-medium shadow-sm hover:shadow-md hover:shadow-blue-500/20 transition-all duration-300 hover:scale-105"
                      >
                        <Copy className="size-3"/>
                        Copy
                      </Button>
                      <Button
                        onClick={() => resend(_id)}
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-1.5 bg-white/80 dark:bg-gray-700/80 backdrop-blur-sm text-gray-600 dark:text-gray-300 border-gray-200/60 dark:border-gray-600/60 hover:border-green-300/60 dark:hover:border-green-500/60 rounded-lg px-2.5 py-1.5 h-7 text-xs font-medium shadow-sm hover:shadow-md hover:shadow-green-500/20 transition-all duration-300 hover:scale-105"
                      >
                        <RefreshCcw className="size-3"/>
                        Retry
                      </Button>
                  </div>
              }
            </div>
        }
      </div>;
    default:
      return <></>;
  }
}


const ChatInput = React.memo(function ChatInput(props: {
  value: string;
  isLoading: boolean;
  onChange: (value: string) => void;
  onSubmit: (e: React.FormEvent) => void;
  onStop: () => void;
}) {
  const {value, isLoading, onChange, onStop, onSubmit} = props

  return <div className="w-full md:w-2/3 lg:w-1/2 m-auto px-4">
    <form
      onSubmit={onSubmit}
      className="flex items-center bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border border-gray-200/60 dark:border-gray-700/60 rounded-xl md:rounded-2xl px-3 py-2.5 md:px-5 md:py-3 shadow-xl shadow-black/10 dark:shadow-black/30 hover:shadow-xl transition-all duration-300 hover:border-blue-300/60 dark:hover:border-blue-600/60"
    >
      <Input
        type="text"
        className="border-none text-gray-800 dark:text-gray-100 text-sm md:text-base mr-2.5 placeholder:text-gray-500 dark:placeholder:text-gray-400 font-medium"
        autoSize
        value={value}
        onChange={(value) => onChange(value)}
        placeholder="Ask anything about Web3, or crypto..."
      />

      {
        !isLoading && <Button
              type="submit"
              className="flex items-center justify-center cursor-pointer size-9 md:size-10 rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 p-0 shadow-lg shadow-blue-500/25 hover:shadow-blue-500/40 transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
              disabled={!value.trim() || isLoading}
          >
              <svg className="size-4 md:size-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
              </svg>
          </Button>
      }

      {
        isLoading && <motion.div
              animate={{opacity: [0.5, 1, 0.5]}}
              transition={{repeat: Infinity, duration: 1.5}}
              className="flex items-center gap-2"
          >
              <Button
                  type="button"
                  className="flex items-center justify-center cursor-pointer size-9 md:size-10 rounded-lg bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 p-0 shadow-lg shadow-red-500/25 hover:shadow-red-500/40 transition-all duration-300 hover:scale-105"
                  onClick={onStop}
              >
                  <CircleStop className="size-4 md:size-5 text-white"/>
              </Button>
          </motion.div>
      }
    </form>
  </div>
})


export default function Home() {
  const account = useAccount()
  const {openConnectModal} = useConnectModal()
  const {connectAsync, connectors} = useConnect();
  const [presetAddress, setPresetAddress] = useState<string>()
  const [hasStartedChat, setHasStartedChat] = useState(false);
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [abortController, setAbortController] = useState<AbortController | null>(null);
  const {messages, appendMsg, updateMsg, resetList} = useMessages();
  const chatRef = useRef<MessageContainerHandle>(null)

  useEffect(() => {
    window.presetAddress = (address: string) => {
      if (address) {
        setPresetAddress(address);
        setHasStartedChat(true)
      }
    }
    window.receiveMessageFromNative = (msg, address) => {
      sendChatMessage(msg, address, false)
    }
  }, [])

  function connectWallet() {
    if (account.address) return
    const injectedConnector = connectors.find((c) => c.id === 'iopay');
    console.log('connector', injectedConnector);
    if (injectedConnector) {
      connectAsync({connector: injectedConnector});
    } else {
      openConnectModal();
    }
  }

  async function sendChatMessage(messageText: string, address: string, resend: boolean, id?: string) {
    if (!resend && (!messageText.trim() || isLoading)) return;
    const history = messages.map(msg => {
      return {
        role: msg.type,
        content: msg.content.text
      }
    })
    setInput("");
    setIsLoading(true);
    setHasStartedChat(true)

    let question = messageText
    const replyId = Date.now().toString()
    if (!resend || messages.length < 0) {
      appendMsg({
        type: "user",
        hasTime: false,
        content: {text: question},
        position: "right",
      });
      appendMsg({
        _id: replyId,
        type: "assistant",
        hasTime: false,
        content: {},
        user: {
          avatar: '/images/binoai-logo.svg',
        },
      });
    } else {
      const index = messages.findIndex(msg => msg._id === id)
      if (index != -1) {
        const list = messages.slice(0, index)
        resetList(list)
        question = list[list.length - 1].content.text
        appendMsg({
          _id: replyId,
          type: "assistant",
          hasTime: false,
          content: {},
          user: {
            avatar: '/images/binoai-logo.svg',
          },
        });
      } else {
        return
      }
    }
    try {
      const controller = new AbortController();
      setAbortController(controller);
      const readableStream = await sendChat(question, history, address)
      const reader = readableStream.getReader();
      const decoder = new TextDecoder();
      let assistantResponse = "";
      const throttledUpdateMsg = throttle(updateMsg, 1500, {trailing: true});
      const parser = createParser({
        onEvent: (event: EventSourceMessage) => {
          try {
            const data = event.data;
            const jsonData = JSON.parse(data);
            if (!isLoading && jsonData.length > 0) {
              const eventItem = jsonData[0];
              console.log("eventItem: ", eventItem.type)
              switch (eventItem.type) {
                case "text-delta":
                  const newText = eventItem.textDelta || "";
                  assistantResponse += newText;
                  throttledUpdateMsg(replyId, {
                    type: "assistant",
                    content: {text: assistantResponse, finish: false},
                    user: {
                      avatar: '/images/binoai-logo.svg',
                    },
                  })
                  break;
                case "finish":
                  throttledUpdateMsg(
                    replyId, {
                      type: "assistant",
                      content: {text: assistantResponse, finish: true},
                      user: {
                        avatar: '/images/binoai-logo.svg',
                      },
                    }
                  )
                  break;
                default:
                  console.log("Unknown event type:", eventItem.type);
              }
            }
          } catch (error) {
            console.error("Error processing event:", error);
          }
        },
      });
      while (true) {
        const {done, value} = await reader.read();
        if (done) break;
        const chunk = decoder.decode(value, {stream: true});
        parser.feed(chunk);
      }

      setIsLoading(false);
      setAbortController(null);
    } catch (error) {
      if (error.name === "AbortError") {
        console.log("Request was aborted");
      } else {
        console.error("Chat request error:", error);
      }
      setIsLoading(false);
      setAbortController(null);
    }
  }

  function handleSubmit(question: string, resend: boolean, id?: string) {
    const walletAddress = account.address || presetAddress
    if (!walletAddress) {
      connectWallet()
      return
    }
    sendChatMessage(question, walletAddress, resend, id);
  }

  const stopResponseGeneration = useCallback(() => {
    if (abortController) {
      abortController.abort();
      setIsLoading(false);
      setAbortController(null);
    }
  }, [abortController]);

  function renderDashboard() {
    return <AnimatePresence>
      <motion.div
        initial={{opacity: 0}}
        animate={{opacity: 1}}
        exit={{opacity: 0}}
        transition={{duration: 0.8}}
        className="min-h-screen relative overflow-hidden"
      >
        {/* Background decorative elements */}
        <div
          className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-purple-50/30 dark:from-blue-950/20 dark:via-transparent dark:to-purple-950/20"></div>
        <div
          className="absolute top-20 left-10 w-32 h-32 bg-blue-200/20 dark:bg-blue-800/20 rounded-full blur-3xl"></div>
        <div
          className="absolute bottom-20 right-10 w-40 h-40 bg-purple-200/20 dark:bg-purple-800/20 rounded-full blur-3xl"></div>
        <div
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-blue-100/10 to-purple-100/10 dark:from-blue-900/10 dark:to-purple-900/10 rounded-full blur-3xl"></div>

        {/* Main content container */}
        <div className="relative z-10 h-screen flex items-center justify-center flex-col px-4 py-4 md:py-6">
          {/* Header section with logo and title */}
          <motion.div
            initial={{y: -30, opacity: 0}}
            animate={{y: 0, opacity: 1}}
            transition={{duration: 0.8, ease: "easeOut"}}
            className="text-center mb-6 md:mb-8"
          >
            {/* Logo with enhanced styling */}
            <motion.div
              initial={{scale: 0.8, opacity: 0}}
              animate={{scale: 1, opacity: 1}}
              transition={{duration: 0.6, delay: 0.2}}
              className="inline-flex items-center justify-center mb-4"
            >
              <div className="relative">
                <div
                  className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full blur-xl opacity-30 animate-pulse"></div>
                <img
                  className="size-16 md:size-20 lg:size-24 relative z-10 drop-shadow-lg"
                  src="/images/binoai-logo.svg" alt="BinoAI Logo"
                />
              </div>
            </motion.div>

            {/* Title with gradient text */}
            <motion.div
              initial={{y: 20, opacity: 0}}
              animate={{y: 0, opacity: 1}}
              transition={{duration: 0.6, delay: 0.4}}
              className="mb-3"
            >
              <h1
                className="text-2xl md:text-4xl lg:text-5xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 dark:from-white dark:via-blue-200 dark:to-purple-200 bg-clip-text text-transparent leading-tight">
                ioPay BinoAI
              </h1>
            </motion.div>

            {/* Subtitle with better typography */}
            <motion.p
              initial={{y: 20, opacity: 0}}
              animate={{y: 0, opacity: 1}}
              transition={{duration: 0.6, delay: 0.6}}
              className="text-gray-600 dark:text-gray-300 max-w-xl mx-auto text-base md:text-lg font-medium leading-relaxed mb-5"
            >
              Your intelligent companion for seamless Web3 exploration and crypto insights
            </motion.p>

            {/* Connect wallet button with enhanced styling */}
            <motion.div
              initial={{y: 20, opacity: 0}}
              animate={{y: 0, opacity: 1}}
              transition={{duration: 0.6, delay: 0.8}}
              className="mb-5"
            >
              <ConnectWallet/>
            </motion.div>
          </motion.div>

          {/* Chat input section */}
          <motion.div
            initial={{y: 30, opacity: 0}}
            animate={{y: 0, opacity: 1}}
            transition={{duration: 0.8, delay: 0.4, ease: "easeOut"}}
            className="w-full mx-auto mb-6 md:mb-8"
          >
            <ChatInput
              value={input}
              onChange={(value) => setInput(value)}
              onSubmit={(e) => {
                e.preventDefault();
                handleSubmit(input, false)
              }}
              onStop={stopResponseGeneration}
              isLoading={isLoading}
            />
          </motion.div>

          {/* Carousel section with enhanced container */}
          <motion.div
            initial={{y: 30, opacity: 0}}
            animate={{y: 0, opacity: 1}}
            transition={{duration: 0.8, delay: 0.6, ease: "easeOut"}}
            className="w-full max-w-4xl mx-auto"
          >
            <div
              className="bg-white/40 dark:bg-black/20 backdrop-blur-sm rounded-xl p-3 md:p-4 border border-gray-200/50 dark:border-gray-700/50 shadow-xl shadow-black/5 dark:shadow-black/20">
              <div className="text-center mb-3">
                <h3 className="text-xs md:text-sm font-semibold text-gray-700 dark:text-gray-300 mb-1.5">
                  Popular Questions
                </h3>
                <div className="w-12 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto rounded-full"></div>
              </div>
              <Carousel onItemClick={(question) => handleSubmit(question, false)}/>
            </div>
          </motion.div>
        </div>
      </motion.div>
    </AnimatePresence>
  }

  function renderChatPage() {

    return <div
      className="h-screen flex flex-col relative overflow-hidden"
    >
      {/* Background decorative elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/20 via-transparent to-purple-50/20 dark:from-blue-950/10 dark:via-transparent dark:to-purple-950/10"></div>
      <div className="absolute top-20 left-10 w-32 h-32 bg-blue-200/10 dark:bg-blue-800/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-20 right-10 w-40 h-40 bg-purple-200/10 dark:bg-purple-800/10 rounded-full blur-3xl"></div>

      {/* Main chat container */}
      <div className="relative z-10 flex flex-col h-full">
        <Chat
          messages={messages}
          wideBreakpoint="768px"
          messagesRef={chatRef}
          renderNavbar={() => {
            return presetAddress
              ? <></>
              : <Navbar
                onBackHome={() => {
                  resetList()
                  setHasStartedChat(false)
                }}
                onClear={() => resetList()}
              />
          }}
          renderMessageContent={(msg) => (
            <MessageContent
              {...msg}
              isLoading={isLoading}
              onRender={() => {
                chatRef.current?.scrollToEnd({animated: true})
              }}
              resend={(id) => {
                handleSubmit("", true, id)
              }}
            />
          )}
          onSend={() => {
          }}
          Composer={() => <></>}
        />

        {/* Chat input section with enhanced styling */}
        <div className="bg-white/40 dark:bg-black/20 backdrop-blur-sm border-t border-gray-200/50 dark:border-gray-700/50 px-4 py-4 md:px-6 md:py-6">
          <ChatInput
            value={input}
            onChange={(value) => setInput(value)}
            onSubmit={(e) => {
              e.preventDefault()
              handleSubmit(input, false)
            }}
            onStop={stopResponseGeneration}
            isLoading={isLoading}
          />
        </div>
      </div>
    </div>
  }

  return (
    <>
      {
        hasStartedChat
          ? renderChatPage()
          : renderDashboard()
      }
    </>
  );
}
