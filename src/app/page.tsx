'use client';

import Chat, { Bubble, Input, Typing, TypingBubble, useMessages } from "@chatui/core";
import { useDarkTheme } from "@/hooks/useDarkTheme";
import { ArrowLeft, CircleStop, Copy, RefreshCcw, Trash2 } from "lucide-react";
import { marked } from "marked";
import { MessageProps } from "@chatui/core/lib/components/Message/Message";
import "@chatui/core/dist/index.css";
import "./_assets/chatui.css"
import "./_assets/markdown.css"
import React, { useCallback, useEffect, useRef, useState } from "react";
import { AnimatePresence, motion } from "framer-motion";
import { useAccount, useConnect } from "wagmi";
import { useConnectModal } from "@rainbow-me/rainbowkit";
import { sendChat } from "@/action";
import { createParser, EventSourceMessage } from "eventsource-parser";
import { ConnectWallet } from "@/components/ConnectWallet/ConnectWallet";
import { Button } from "@/components/Button/button";
import { MessageContainerHandle } from "@chatui/core/lib/components/MessageContainer";
import Prism from "prismjs";
import 'prismjs/themes/prism-tomorrow.css';
import toast from "react-hot-toast";
import Carousel from "@/components/carousel";
import { throttle } from 'lodash';

marked.use({
  async: false,
  extensions: [{
    name: "code",
    level: 'block',
    renderer: function (token) {
      const {lang, text} = token
      const language = Prism.languages[lang] ? lang : 'plaintext'
      const highlighted = Prism.languages[language]
        ? Prism.highlight(text, Prism.languages[language], language)
        : token.text;
      return `<pre class="language-${language}"><code>${highlighted}</code></pre>`;
    }
  }],
  walkTokens(token) {
    if (token.type === 'code') {
      return (async () => {
        if (token.lang && !Prism.languages[token.lang]) {
          await import(`prismjs/components/prism-${token.lang}`);
        }
      })();
    }
  }
})

function Navbar(props: {
  onBackHome: () => void,
  onClear: () => void
}) {
  const isDarkMode = useDarkTheme()
  return <div className="">
    <div
      className="hidden bg-[#F7F7F9] dark:bg-[#FFFFFF38] md:flex items-center justify-between px-8 py-2.5 border-none">
      <div className="flex items-center gap-3 cursor-pointer" onClick={props.onBackHome}>
        <div
          className="hidden md:inline-flex items-center justify-center cursor-pointer"
        >
          <img className="size-8" src="/images/binoai-logo.svg" alt=""/>
        </div>
        <div className="cursor-pointer ml-1">
          <div className="text-sm font-bold text-black dark:text-white">
            ioPay BinoAI
          </div>
          <div className="text-xs text-[#00000099] dark:text-[#FFFFFF99]">
            Your Crypto AI Agent
          </div>
        </div>
      </div>
      <Trash2 color={`${isDarkMode ? 'white' : 'black'}`} className="size-5 cursor-pointer" onClick={props.onClear}/>
    </div>
    <div className="flex md:hidden items-center justify-between h-12 px-4">
      <ArrowLeft color={isDarkMode ? 'white' : 'black'} className="size-6 cursor-pointer" onClick={props.onBackHome}/>
      <div className="text-black dark:text-white">ioPay BinoAI</div>
      <div className="size-6"></div>
    </div>
  </div>
}

function MessageContent(props: MessageProps & {
  isLoading: boolean,
  onRender?: () => void,
  resend?: (id: string) => void
}) {
  const {_id, type, content, isLoading, onRender, resend} = props;
  const isDarkMode = useDarkTheme()

  const throttledOnRender = throttle(onRender, 2000, {trailing: true});

  function renderMarkdown(text) {
    const html = marked(text, {
      async: false,
      gfm: true,
      breaks: true,
    })
    throttledOnRender()
    return html;
  }

  switch (type) {
    case "user":
      return <Bubble
        className="break-words px-4 py-1.5 bg-[#F4F4F4] rounded-full text-black whitespace-pre-wrap"
      >
        {content.text}
      </Bubble>;
    case "assistant":
      return <div className="binoai-markdown">
        {
          !content.text
            ? (isLoading && <Typing text="Thinking"/>)
            : <div>
              {
                isLoading
                  ? <TypingBubble
                    className="text-black dark:text-white py-1"
                    content={content.text}
                    isRichText
                    options={{
                      interval: 50,
                      step: [4, 8],
                      initialIndex: 10
                    }}
                    messageRender={renderMarkdown}
                  />
                  : <div dangerouslySetInnerHTML={{ __html: renderMarkdown(content.text) }} />
              }
              {
                (content.finish || !isLoading) &&
                  <div className="flex items-center gap-3">
                      <Copy
                          color={`${isDarkMode ? 'white' : 'black'}`}
                          className="size-4 cursor-pointer"
                          onClick={async () => {
                            await navigator.clipboard.writeText(content.text);
                            toast.success("Copied!");
                          }}
                      />
                      <RefreshCcw
                          color={`${isDarkMode ? 'white' : 'black'}`}
                          className="size-4 cursor-pointer"
                          onClick={() => resend(_id)}
                      />
                  </div>
              }
            </div>
        }
      </div>;
    default:
      return <></>;
  }
}


const ChatInput = React.memo(function ChatInput(props: {
  value: string;
  isLoading: boolean;
  onChange: (value: string) => void;
  onSubmit: (e: React.FormEvent) => void;
  onStop: () => void;
}) {
  const {value, isLoading, onChange, onStop, onSubmit} = props

  return <div className="w-full md:w-1/2 m-auto px-4">
    <form
      onSubmit={onSubmit}
      className="flex items-center shadow-md shadow-black/10 dark:shadow-none border-[1px] border-[#B9B9B980] dark:border-none bg-[#FFFFFF38] rounded-4xl px-3 py-1 md:rounded-3xl md:px-4 md:py-3"
    >
      <Input
        type="text"
        className="border-none text-black dark:text-white text-lg mr-2"
        autoSize
        value={value}
        onChange={(value) => onChange(value)}
        placeholder="Ask any questions about Web3..."
      />

      {
        !isLoading && <Button
              type="submit"
              className="flex items-center justify-center cursor-pointer size-8 rounded-xl bg-transparent hover:bg-transparent p-0 "
              disabled={!value.trim() || isLoading}
          >
              <img className="size-8" src="/images/send.svg" alt=""/>
          </Button>
      }

      {
        isLoading && <motion.div
              animate={{opacity: [0.5, 1, 0.5]}}
              transition={{repeat: Infinity, duration: 1.5}}
              className="flex items-center gap-2"
          >
              <Button
                  type="submit"
                  className="flex items-center justify-center cursor-pointer size-8 rounded-full bg-black hover:bg-transparent p-0 "
                  onClick={onStop}
              >
                  <CircleStop color="#f43f5e"/>
              </Button>
          </motion.div>
      }
    </form>
  </div>
})


export default function Home() {
  const account = useAccount()
  const {openConnectModal} = useConnectModal()
  const {connectAsync, connectors} = useConnect();
  const [presetAddress, setPresetAddress] = useState<string>()
  const [hasStartedChat, setHasStartedChat] = useState(false);
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [abortController, setAbortController] = useState<AbortController | null>(null);
  const {messages, appendMsg, updateMsg, resetList} = useMessages();
  const chatRef = useRef<MessageContainerHandle>(null)

  useEffect(() => {
    window.presetAddress = (address: string) => {
      if (address) {
        setPresetAddress(address);
        setHasStartedChat(true)
      }
    }
    window.receiveMessageFromNative = (msg, address) => {
      sendChatMessage(msg, address, false)
    }
  }, [])

  function connectWallet() {
    if (account.address) return
    const injectedConnector = connectors.find((c) => c.id === 'iopay');
    console.log('connector', injectedConnector);
    if (injectedConnector) {
      connectAsync({connector: injectedConnector});
    } else {
      openConnectModal();
    }
  }

  async function sendChatMessage(messageText: string, address: string, resend: boolean, id?: string) {
    if (!resend && (!messageText.trim() || isLoading)) return;
    const history = messages.map(msg => {
      return {
        role: msg.type,
        content: msg.content.text
      }
    })
    setInput("");
    setIsLoading(true);
    setHasStartedChat(true)

    let question = messageText
    const replyId = Date.now().toString()
    if (!resend || messages.length < 0) {
      appendMsg({
        type: "user",
        hasTime: false,
        content: {text: question},
        position: "right",
      });
      appendMsg({
        _id: replyId,
        type: "assistant",
        hasTime: false,
        content: {},
        user: {
          avatar: '/images/binoai-logo.svg',
        },
      });
    } else {
      const index = messages.findIndex(msg => msg._id === id)
      if (index != -1) {
        const list = messages.slice(0, index)
        resetList(list)
        question = list[list.length - 1].content.text
        appendMsg({
          _id: replyId,
          type: "assistant",
          hasTime: false,
          content: {},
          user: {
            avatar: '/images/binoai-logo.svg',
          },
        });
      } else {
        return
      }
    }
    try {
      const controller = new AbortController();
      setAbortController(controller);
      const readableStream = await sendChat(question, history, address)
      const reader = readableStream.getReader();
      const decoder = new TextDecoder();
      let assistantResponse = "";
      const throttledUpdateMsg = throttle(updateMsg, 1500, {trailing: true});
      const parser = createParser({
        onEvent: (event: EventSourceMessage) => {
          try {
            const data = event.data;
            const jsonData = JSON.parse(data);
            if (!isLoading && jsonData.length > 0) {
              const eventItem = jsonData[0];
              console.log("eventItem: ", eventItem.type)
              switch (eventItem.type) {
                case "text-delta":
                  const newText = eventItem.textDelta || "";
                  assistantResponse += newText;
                  throttledUpdateMsg(replyId, {
                    type: "assistant",
                    content: {text: assistantResponse, finish: false},
                    user: {
                      avatar: '/images/binoai-logo.svg',
                    },
                  })
                  break;
                case "finish":
                  throttledUpdateMsg(
                    replyId, {
                      type: "assistant",
                      content: {text: assistantResponse, finish: true},
                      user: {
                        avatar: '/images/binoai-logo.svg',
                      },
                    }
                  )
                  break;
                default:
                  console.log("Unknown event type:", eventItem.type);
              }
            }
          } catch (error) {
            console.error("Error processing event:", error);
          }
        },
      });
      while (true) {
        const {done, value} = await reader.read();
        if (done) break;
        const chunk = decoder.decode(value, {stream: true});
        parser.feed(chunk);
      }

      setIsLoading(false);
      setAbortController(null);
    } catch (error) {
      if (error.name === "AbortError") {
        console.log("Request was aborted");
      } else {
        console.error("Chat request error:", error);
      }
      setIsLoading(false);
      setAbortController(null);
    }
  }

  function handleSubmit(question: string, resend: boolean, id?: string) {
    const walletAddress = account.address || presetAddress
    if (!walletAddress) {
      connectWallet()
      return
    }
    sendChatMessage(question, walletAddress, resend, id);
  }

  const stopResponseGeneration = useCallback(() => {
    if (abortController) {
      abortController.abort();
      setIsLoading(false);
      setAbortController(null);
    }
  }, [abortController]);

  function renderDashboard() {
    return <AnimatePresence>
      <motion.div
        initial={{opacity: 0}}
        animate={{opacity: 1}}
        exit={{opacity: 0}}
        transition={{duration: 0.5}}
        className="h-screen flex items-center justify-center flex-col"
      >
        <motion.div
          initial={{y: -20, opacity: 0}}
          animate={{y: 0, opacity: 1}}
          transition={{duration: 0.5}}
          className="text-center mb-8"
        >
          <div
            className="inline-flex items-center justify-center">
            <img className="size-20" src="/images/binoai-logo.svg" alt=""/>
          </div>
          <div
            className="text-black dark:text-white text-2xl md:text-4xl font-semibold mt-2 font-roboto"
          >
            ioPay BinoAI
          </div>

          <p className="text-[#00000099] dark:text-[#FFFFFF80] max-w-2xl mt-2 mx-auto text-lg">
            AI-optimized Web3 exploration
          </p>
          <div className="mt-4">
            <ConnectWallet/>
          </div>
        </motion.div>

        <motion.div
          initial={{y: 20, opacity: 0}}
          animate={{y: 0, opacity: 1}}
          transition={{delay: 0.2}}
          className="w-full mx-auto"
        >
          <ChatInput
            value={input}
            onChange={(value) => setInput(value)}
            onSubmit={(e) => {
              e.preventDefault();
              handleSubmit(input, false)
            }}
            onStop={stopResponseGeneration}
            isLoading={isLoading}
          />
        </motion.div>

        <motion.div
          initial={{y: 20, opacity: 0}}
          animate={{y: 0, opacity: 1}}
          transition={{delay: 0.2}}
          className="w-full max-w-4xl mx-auto mt-4"
        >
          <Carousel onItemClick={(question) => handleSubmit(question, false)}/>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  }

  function renderChatPage() {

    return <div
      className="h-screen flex flex-col"
    >
      <Chat
        messages={messages}
        wideBreakpoint="768px"
        messagesRef={chatRef}
        renderNavbar={() => {
          return presetAddress
            ? <></>
            : <Navbar
              onBackHome={() => {
                resetList()
                setHasStartedChat(false)
              }}
              onClear={() => resetList()}
            />
        }}
        renderMessageContent={(msg) => (
          <MessageContent
            {...msg}
            isLoading={isLoading}
            onRender={() => {
              chatRef.current?.scrollToEnd({animated: true})
            }}
            resend={(id) => {
              handleSubmit("", true, id)
            }}
          />
        )}
        onSend={() => {
        }}
        Composer={() => <></>}
      />
      <div className="pb-8">
        <ChatInput
          value={input}
          onChange={(value) => setInput(value)}
          onSubmit={(e) => {
            e.preventDefault()
            handleSubmit(input, false)
          }}
          onStop={stopResponseGeneration}
          isLoading={isLoading}
        />
      </div>
    </div>
  }

  return (
    <>
      {
        hasStartedChat
          ? renderChatPage()
          : renderDashboard()
      }
    </>
  );
}
