import { Autoplay, EffectFade } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import React from "react";
import { cn } from "@/lib/utils";
import 'swiper/css';
import 'swiper/css/effect-fade';

const FREQUENTLY_QUESTIONS = [
  "What is DePIN and which is the most popular on IoTeX?",
  "What is IOTX?",
  "What is ioPay?",
  "What is ioPay BinoAI?",
  "How do I stake my IOTX on IoTeX?",
  "Which DEXs are available on IoTeX?",
  "How can I earn rewards on IoTeX?",
  "How do I use ioTube?",
  "Which team designed you (this AI)?"
]

const QUESTIONS_MAP = [
  [
    "What is DePIN and which is the most popular on IoTeX?",
    "What is IOTX?",
    "What is ioPay?",
  ],
  [
    "What is ioPay BinoAI?",
    "How do I stake my IOTX on IoTeX?",
    "Which DEXs are available on IoTeX?",
  ],
  [
    "How can I earn rewards on IoTeX?",
    "How do I use ioTube?",
    "Which team designed you (this AI)?"
  ]
]

export default function Carousel(props: { onItemClick?: (question: string) => void }) {

  const Page = ({className, questions}: { className?: string, questions: string[] }) => {
    return <div
      className="flex items-center justify-center flex-wrap w-full max-w-5xl mx-auto gap-2 md:gap-3">
      {
        questions.map((question, index) => (
          <div
            key={index}
            className={cn(className, "group text-gray-700 dark:text-gray-200 bg-gradient-to-r from-white/90 to-gray-50/90 dark:from-gray-800/90 dark:to-gray-900/90 backdrop-blur-sm border border-gray-200/60 dark:border-gray-700/60 rounded-full text-xs md:text-sm px-3 md:px-5 py-2 md:py-2.5 shadow-lg shadow-black/5 dark:shadow-black/20 hover:shadow-xl hover:shadow-blue-500/20 dark:hover:shadow-blue-500/30 cursor-pointer transition-all duration-300 hover:scale-105 hover:border-blue-300/60 dark:hover:border-blue-600/60 hover:bg-gradient-to-r hover:from-blue-50/90 hover:to-purple-50/90 dark:hover:from-blue-900/50 dark:hover:to-purple-900/50 font-medium")}
            onClick={() => props.onItemClick(question)}>
            <span className="group-hover:text-blue-700 dark:group-hover:text-blue-300 transition-colors duration-300">
              {question}
            </span>
          </div>
        ))
      }
    </div>
  }


  return <div className="max-w-full mx-auto">

    <Page className="hidden md:flex" questions={FREQUENTLY_QUESTIONS}/>

    <div className="md:hidden">
      <Swiper
        modules={[Autoplay, EffectFade]}
        loop
        effect="fade"
        speed={1000}
        autoplay={{
          delay: 6000,
          disableOnInteraction: false,
        }}
        className="rounded-xl overflow-hidden"
      >
        {
          QUESTIONS_MAP.map((questions, i) => (
            <SwiperSlide key={i} className="bg-transparent py-2">
              <Page questions={questions}/>
            </SwiperSlide>
          ))
        }
      </Swiper>
    </div>

  </div>
}