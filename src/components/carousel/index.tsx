import { Autoplay, EffectFade } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import React from "react";
import { cn } from "@/lib/utils";
import 'swiper/css';
import 'swiper/css/effect-fade';

const FREQUENTLY_QUESTIONS = [
  "What is DePIN and which is the most popular on IoTeX?",
  "What is IOTX?",
  "What is ioPay?",
  "What is ioPay BinoAI?",
  "How do I stake my IOTX on IoTeX?",
  "Which DEXs are available on IoTeX?",
  "How can I earn rewards on IoTeX?",
  "How do I use ioTube?",
  "Which team designed you (this AI)?"
]

const QUESTIONS_MAP = [
  [
    "What is DePIN and which is the most popular on IoTeX?",
    "What is IOTX?",
    "What is ioPay?",
  ],
  [
    "What is ioPay BinoAI?",
    "How do I stake my IOTX on IoTeX?",
    "Which DEXs are available on IoTeX?",
  ],
  [
    "How can I earn rewards on IoTeX?",
    "How do I use ioTube?",
    "Which team designed you (this AI)?"
  ]
]

export default function Carousel(props: { onItemClick?: (question: string) => void }) {

  const Page = ({className, questions}: { className?: string, questions: string[] }) => {
    return <div
      className="flex items-center justify-center flex-wrap w-full max-w-4xl mx-auto gap-1.5 md:gap-2.5 ">
      {
        questions.map((question, index) => (
          <div
            key={index}
            className={cn(className, "text-[#000000B2] dark:text-[#FFFFFFCC] shadow-md shadow-black/10 dark:shadow-none border-[1px] border-[#B9B9B980] dark:border-none bg-[#FFFFFF38] rounded-full text-xs md:text-sm px-2 md:px-4 py-1 md:py-1.5 dark:hover:bg-[#FFFFFF80] hover:bg-[#00000020] cursor-pointer")}
            onClick={() => props.onItemClick(question)}>
            {question}
          </div>
        ))
      }
    </div>
  }


  return <div className="max-w-full mx-auto">

    <Page className="hidden md:flex" questions={FREQUENTLY_QUESTIONS}/>

    <div className="md:hidden">
      <Swiper
        modules={[Autoplay, EffectFade]}
        loop
        effect="fade"
        speed={800}
        autoplay={{
          delay: 5000,
          disableOnInteraction: false,
        }}
      >
        {
          QUESTIONS_MAP.map((questions, i) => (
            <SwiperSlide key={i} className="bg-white dark:bg-black">
              <Page questions={questions}/>
            </SwiperSlide>
          ))
        }
      </Swiper>
    </div>

  </div>
}