'use client';

import { ConnectButton } from '@rainbow-me/rainbowkit';
import { Wallet } from 'lucide-react';
import { Button } from "@/components/Button/button";

export function ConnectWallet() {
  return (
    <ConnectButton.Custom>
      {({
        account,
        chain,
        openAccountModal,
        openChainModal,
        openConnectModal,
        authenticationStatus,
        mounted,
      }) => {
        // Note: If your app doesn't use authentication, you
        // can remove all 'authenticationStatus' checks
        const ready = mounted && authenticationStatus !== 'loading';
        const connected =
          ready &&
          account &&
          chain &&
          (!authenticationStatus || authenticationStatus === 'authenticated');

        return (
          <div
            {...(!ready && {
              'aria-hidden': true,
              style: {
                opacity: 0,
                pointerEvents: 'none',
                userSelect: 'none',
              },
            })}
          >
            {(() => {
              if (!connected) {
                return (
                  <Button
                    onClick={openConnectModal}
                    size="lg"
                    className="bg-gradient-to-r from-blue-500 via-blue-600 to-purple-600 hover:from-blue-600 hover:via-blue-700 hover:to-purple-700 text-white rounded-2xl px-8 py-3 h-12 shadow-xl shadow-blue-500/30 hover:shadow-blue-500/50 transition-all duration-300 hover:scale-105 font-semibold text-base backdrop-blur-sm border border-blue-400/20"
                  >
                    <Wallet className="mr-3 h-5 w-5" />
                    Connect Wallet
                  </Button>
                );
              }

              if (chain.unsupported) {
                return (
                  <Button
                    onClick={openChainModal}
                    size="lg"
                    className="bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 text-white rounded-2xl px-6 py-3 h-12 shadow-xl shadow-red-500/30 hover:shadow-red-500/50 transition-all duration-300 hover:scale-105 font-semibold border border-red-400/20"
                  >
                    Wrong Network
                  </Button>
                );
              }

              return (
                <div className="flex gap-3 items-center flex-wrap justify-center">
                  <Button
                    onClick={openChainModal}
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-2 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm text-gray-700 dark:text-gray-200 border-gray-200/60 dark:border-gray-700/60 hover:border-blue-300/60 dark:hover:border-blue-600/60 rounded-xl px-4 py-2 h-10 text-sm font-medium shadow-lg shadow-black/5 dark:shadow-black/20 hover:shadow-xl hover:shadow-blue-500/20 transition-all duration-300 hover:scale-105"
                  >
                    {chain.hasIcon && (
                      <div
                        style={{
                          background: chain.iconBackground,
                          width: 18,
                          height: 18,
                          borderRadius: 999,
                          overflow: 'hidden',
                        }}
                        className="shadow-sm"
                      >
                        {chain.iconUrl && (
                          <img
                            alt={chain.name ?? 'Chain icon'}
                            src={chain.iconUrl}
                            style={{ width: 18, height: 18 }}
                          />
                        )}
                      </div>
                    )}
                    <span className="font-semibold">{chain.name}</span>
                  </Button>

                  <Button
                    onClick={openAccountModal}
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-2 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm text-gray-700 dark:text-gray-200 border-gray-200/60 dark:border-gray-700/60 hover:border-green-300/60 dark:hover:border-green-600/60 rounded-xl px-4 py-2 h-10 text-sm font-medium shadow-lg shadow-black/5 dark:shadow-black/20 hover:shadow-xl hover:shadow-green-500/20 transition-all duration-300 hover:scale-105"
                  >
                    <div className="w-2.5 h-2.5 rounded-full bg-green-400 animate-pulse shadow-sm"></div>
                    <span className="font-semibold">{account.displayName}</span>
                    {account.displayBalance && (
                      <span className="text-xs text-gray-500 dark:text-gray-400 font-normal">
                        ({account.displayBalance})
                      </span>
                    )}
                  </Button>
                </div>
              );
            })()}
          </div>
        );
      }}
    </ConnectButton.Custom>
  );
} 