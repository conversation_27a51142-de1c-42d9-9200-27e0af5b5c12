'use client';

import React, { PropsWithChildren, Suspense } from "react";
import { useDidMount } from "@/hooks/useDidMount";
import { WagmiProvider } from "wagmi";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { darkTheme, getDefaultConfig, RainbowKitProvider } from "@rainbow-me/rainbowkit";
import { ThemeProvider } from "next-themes";
import { iotex } from "wagmi/chains";
import {
  coinbaseWallet,
  iopayWallet,
  metaMaskWallet,
  rainbowWallet,
  walletConnectWallet
} from "@rainbow-me/rainbowkit/wallets";
import { useSearchParams } from "next/navigation";
import "@/../src/app/_assets/loader.css"
import { Toaster } from "react-hot-toast";
import { cn } from "@/lib/utils";

const config = getDefaultConfig({
  appName: 'binoAI',
  projectId: 'b43244c913bda2ee12c37a9dff383e1e',
  chains: [iotex],
  ssr: true,
  wallets: [{
    groupName: 'Suggested',
    wallets: [
      metaMaskWallet,
      iopayWallet,
      rainbowWallet,
      coinbaseWallet,
      walletConnectWallet,
    ],
  }]
});

const queryClient = new QueryClient();

function RootInner({children}: PropsWithChildren<object>) {
  const params = useSearchParams();
  const theme = params.get("theme") || "dark";

  return (
    <ThemeProvider
      attribute="class"
      defaultTheme={theme}
      forcedTheme={theme}
      enableSystem={false}
      disableTransitionOnChange
    >
      <WagmiProvider config={config}>
        <QueryClientProvider client={queryClient}>
          <RainbowKitProvider theme={darkTheme()}>
            {children}
            <Toaster />
          </RainbowKitProvider>
        </QueryClientProvider>
      </WagmiProvider>
    </ThemeProvider>
  );
}

function Loading() {
  let theme = "dark"
  if (typeof window !== "undefined") {
    const url = new URL(window.location.href);
    theme = url.searchParams.get("theme") || "dark";
  }

  return (
    <div className={cn(
      "h-screen flex items-center justify-center relative overflow-hidden",
      theme === "dark" ? "bg-[#0f0f0f]" : "bg-white",
    )}>
      {/* Background decorative elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/10 via-transparent to-purple-50/10 dark:from-blue-950/5 dark:via-transparent dark:to-purple-950/5"></div>
      <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-blue-200/5 dark:bg-blue-800/5 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute bottom-1/4 right-1/4 w-40 h-40 bg-purple-200/5 dark:bg-purple-800/5 rounded-full blur-3xl animate-pulse"></div>

      {/* Main loading content */}
      <div className="loading-container relative z-10">
        {/* Logo with animation */}
        <div className="loading-logo">
          <img
            src="/images/binoai-logo.svg"
            alt="BinoAI"
            className="w-full h-full object-contain relative z-10"
          />
        </div>

        {/* Loading dots */}
        <div className="loading-dots">
          <div className="loading-dot"></div>
          <div className="loading-dot"></div>
          <div className="loading-dot"></div>
        </div>

        {/* Loading text */}
        <div className="loading-text">
          Initializing BinoAI...
        </div>

        {/* Progress bar */}
        <div className="loading-progress"></div>
      </div>
    </div>
  );
}

export default function Root(props: PropsWithChildren<object>) {
  const didMount = useDidMount();
  return didMount ? (
    <RootInner {...props}/>
  ) : <Loading />
}
