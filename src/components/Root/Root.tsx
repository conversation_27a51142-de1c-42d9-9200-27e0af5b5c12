'use client';

import React, { PropsWithChildren, Suspense } from "react";
import { useDidMount } from "@/hooks/useDidMount";
import { WagmiProvider } from "wagmi";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { darkTheme, getDefaultConfig, RainbowKitProvider } from "@rainbow-me/rainbowkit";
import { ThemeProvider } from "next-themes";
import { iotex } from "wagmi/chains";
import {
  coinbaseWallet,
  iopayWallet,
  metaMaskWallet,
  rainbowWallet,
  walletConnectWallet
} from "@rainbow-me/rainbowkit/wallets";
import { useSearchParams } from "next/navigation";
import "@/../src/app/_assets/loader.css"
import { Toaster } from "react-hot-toast";
import { cn } from "@/lib/utils";

const config = getDefaultConfig({
  appName: 'binoAI',
  projectId: 'b43244c913bda2ee12c37a9dff383e1e',
  chains: [iotex],
  ssr: true,
  wallets: [{
    groupName: 'Suggested',
    wallets: [
      metaMaskWallet,
      iopayWallet,
      rainbowWallet,
      coinbaseWallet,
      walletConnectWallet,
    ],
  }]
});

const queryClient = new QueryClient();

function RootInner({children}: PropsWithChildren<object>) {
  const params = useSearchParams();
  const theme = params.get("theme") || "dark";

  return (
    <ThemeProvider
      attribute="class"
      defaultTheme={theme}
      forcedTheme={theme}
      enableSystem={false}
      disableTransitionOnChange
    >
      <WagmiProvider config={config}>
        <QueryClientProvider client={queryClient}>
          <RainbowKitProvider theme={darkTheme()}>
            {children}
            <Toaster />
          </RainbowKitProvider>
        </QueryClientProvider>
      </WagmiProvider>
    </ThemeProvider>
  );
}

function Loading() {
  let theme = "dark"
  if (typeof window !== "undefined") {
    const url = new URL(window.location.href);
    theme = url.searchParams.get("theme") || "dark";
  }

  return (
    <div className={cn(
      "h-screen flex items-center justify-center",
      theme === "dark" ? "bg-[#0f0f0f]" : "bg-white",
    )}>
      <div className="loader l1"></div>
      <div className="loader l2"></div>
      <div className="loader l3"></div>
      <div className="loader l4"></div>
      <div className="loader l5"></div>
      <div className="loader l6"></div>
      <div className="loader l7"></div>
    </div>
  );
}

export default function Root(props: PropsWithChildren<object>) {
  const didMount = useDidMount();
  return didMount ? (
    <RootInner {...props}/>
  ) : <Loading />
}
