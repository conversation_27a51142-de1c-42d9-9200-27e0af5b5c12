'use server'

import { AgentDelegate } from "@/lib/agents";
import { Message } from "@/types/types";
import * as HyperDX from "@hyperdx/node-opentelemetry";

const agentDelegate = new AgentDelegate();

export async function sendChat(message: string, history: Message[], address: string) {
  try {
    const conversation_id = history?.findLast(msg => msg.role === "assistant")?.conversation_id
    history.push({role: "user", content: message, conversation_id: conversation_id});
    return await agentDelegate.sendMessageToChat(history, address, false);
  } catch (error) {
    console.log("sendChat error: ", error)
    return new ReadableStream({
      start(controller) {
        controller.enqueue(new TextEncoder().encode(`data: [{"type":"text-delta","textDelta":"Something wrong, Please try again! 🥺"}]\n\n`));
        controller.close();
      },
    });
  }
}

export async function logInfo(address: string, words: string, deepThinking: boolean, agent: string) {
  HyperDX.getWinsonTransport().log({
    message: JSON.stringify({ address, deepThinking, words, agent }),
    level: "info"
  }, () => {})
}