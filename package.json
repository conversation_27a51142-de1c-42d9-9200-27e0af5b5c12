{"name": "<PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@chatui/core": "^3.0.0", "@hyperdx/node-opentelemetry": "^0.8.2", "@mastra/core": "^0.10.6", "@mastra/mcp": "^0.10.4", "@mastra/pg": "^0.12.3", "@mastra/pinecone": "^0.11.0", "@mastra/rag": "^1.0.3", "@radix-ui/react-slot": "^1.2.3", "@rainbow-me/rainbowkit": "^2.2.8", "@tanstack/react-query": "^5.81.2", "@types/prismjs": "^1.26.5", "@types/swiper": "^6.0.0", "ai": "^4.3.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "eventsource-parser": "^3.0.2", "framer-motion": "^12.19.1", "lodash": "^4.17.21", "lucide-react": "^0.522.0", "marked": "^15.0.12", "next": "15.3.4", "next-themes": "^0.4.6", "prismjs": "^1.30.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "swiper": "^11.2.8", "tailwind-merge": "^3.3.1", "wagmi": "^2.15.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.4", "postcss": "^8.5.6", "tailwindcss": "^4.1.10", "typescript": "^5"}}